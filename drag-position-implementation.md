# 拖拽按钮位置功能实现

## 功能说明

为MyNav和MyNavPage组件实现了拖拽按钮位置控制功能，用户可以选择拖拽按钮显示在菜单项的左侧或右侧。

## 配置项详情

- **配置名称**: `dragPosition`
- **类型**: `string`
- **可选值**: `'before'` | `'after'`
- **默认值**: `'before'`
- **说明**: 控制拖拽按钮在菜单项中的位置

## 实现方案

### 1. 类型定义
在MyNav和MyNavPage组件中添加了dragPosition属性：

```typescript
/**
 * 拖拽按钮位置 默认在前面 before after
 */
dragPosition?: string;
```

### 2. CSS类名控制
根据dragPosition的值动态添加CSS类名：
- `dragPosition === 'after'` → 添加 `drag-position-after` 类名
- `dragPosition === 'before'` 或默认 → 添加 `drag-position-before` 类名

### 3. CSS样式实现

#### 左侧位置（before - 默认）
```scss
.drag-position-before {
  .cxd-Nav-Menu-item {
    .cxd-Nav-Menu-item-dragBtn {
      order: -1; // 确保拖拽按钮在最前面
      margin-right: 8px;
      margin-left: 0;
    }
  }
}
```

#### 右侧位置（after）
```scss
.drag-position-after {
  .cxd-Nav-Menu-item {
    .cxd-Nav-Menu-item-dragBtn {
      order: 999; // 确保拖拽按钮在最后面
      margin-left: 8px;
      margin-right: 0;
      margin-left: auto; // 推到最右边
    }
    
    // 调整菜单项的布局为flex
    .cxd-Nav-Menu-item-link {
      display: flex !important;
      align-items: center;
      justify-content: space-between;
    }
  }
}
```

## 修改的文件

### 1. 组件类型定义
- `src/renderer/MyNav/MyNav.tsx` - 添加dragPosition属性定义
- `src/renderer/MyNavPage/MyNavPage.tsx` - 添加dragPosition属性定义

### 2. 组件逻辑
- 在render方法中提取dragPosition属性
- 在传递给nav组件的props中包含dragPosition
- 在className中根据dragPosition添加相应的CSS类名

### 3. 样式文件
- `src/renderer/MyNav/MyNav.scss` - 添加拖拽按钮位置控制样式
- `src/renderer/MyNavPage/MyNavPage.scss` - 添加拖拽按钮位置控制样式

### 4. 编辑器插件
- `src/editor/Portlet/MyNav/myNav.tsx` - 已有配置项，添加默认值
- `src/editor/Portlet/MyNavPage/myNavPage.tsx` - 已有配置项，添加默认值

## 使用方法

### 在编辑器中配置
1. 选择MyNav或MyNavPage组件
2. 开启"是否可拖拽排序"
3. 在"拖拽按钮位置"中选择：
   - "靠左" - 拖拽按钮显示在菜单项左侧
   - "靠右" - 拖拽按钮显示在菜单项右侧

### 在代码中配置
```javascript
{
  type: 'my-nav',
  draggable: true,
  dragPosition: 'after', // 'before' | 'after'
  links: [
    {
      label: '菜单项1',
      to: '/page1'
    },
    {
      label: '菜单项2',
      to: '/page2'
    }
  ]
}
```

## 视觉效果

### 左侧位置（before）
```
[⋮⋮] 菜单项1
[⋮⋮] 菜单项2
```

### 右侧位置（after）
```
菜单项1 [⋮⋮]
菜单项2 [⋮⋮]
```

## 技术实现原理

1. **CSS Flexbox布局**: 使用flexbox的order属性控制拖拽按钮的显示顺序
2. **动态类名**: 根据配置动态添加CSS类名来应用不同的样式
3. **边距调整**: 通过margin属性调整拖拽按钮与菜单文本的间距
4. **布局优化**: 在右侧位置时调整菜单项为flex布局，确保拖拽按钮正确对齐

## 兼容性说明

- ✅ 向后兼容：默认值为'before'，不影响现有组件
- ✅ 条件显示：只有在开启拖拽功能时才显示此配置项
- ✅ 统一体验：MyNav和MyNavPage组件使用相同的实现方式
- ✅ 样式隔离：使用特定的CSS类名避免样式冲突

现在用户可以通过"拖拽按钮位置"配置项来控制拖拽按钮在菜单项中的显示位置了！
