<!DOCTYPE html>
<html>
<head>
    <title>测试隐藏展开按钮功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .config-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        pre {
            margin: 0;
            overflow-x: auto;
        }
        code {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>展开按钮隐藏功能测试</h1>
    
    <div class="test-container">
        <div class="test-title">1. 显示展开按钮（默认行为）</div>
        <div class="config-example">
            <pre><code>{
  "type": "my-nav-page",
  "showExpandIcon": true,  // 或者不设置此属性
  "stacked": true,
  "links": [
    {
      "label": "首页",
      "icon": "fa fa-home",
      "to": "/home"
    },
    {
      "label": "产品管理",
      "icon": "fa fa-cube",
      "to": "/products",
      "children": [
        {
          "label": "产品列表",
          "to": "/products/list"
        },
        {
          "label": "产品详情",
          "to": "/products/detail"
        }
      ]
    }
  ]
}</code></pre>
        </div>
        <p><strong>预期效果</strong>：有子菜单的项目会显示展开按钮（箭头图标）</p>
    </div>

    <div class="test-container">
        <div class="test-title">2. 隐藏展开按钮</div>
        <div class="config-example">
            <pre><code>{
  "type": "my-nav-page",
  "showExpandIcon": false,  // 关键配置
  "stacked": true,
  "links": [
    {
      "label": "首页",
      "icon": "fa fa-home",
      "to": "/home"
    },
    {
      "label": "产品管理",
      "icon": "fa fa-cube",
      "to": "/products",
      "children": [
        {
          "label": "产品列表",
          "to": "/products/list"
        },
        {
          "label": "产品详情",
          "to": "/products/detail"
        }
      ]
    }
  ]
}</code></pre>
        </div>
        <p><strong>预期效果</strong>：所有菜单项都不会显示展开按钮，即使有子菜单</p>
    </div>

    <div class="test-container">
        <div class="test-title">3. 编辑器中的配置</div>
        <p><strong>操作步骤</strong>：</p>
        <ol>
            <li>在amis编辑器中选择导航组件</li>
            <li>在右侧属性面板中找到"基本"配置区域</li>
            <li>找到"显示展开按钮"开关</li>
            <li>关闭开关后，"展开按钮位置"和"展开按钮图标"配置项会自动隐藏</li>
            <li>开启开关后，相关配置项会重新显示</li>
        </ol>
    </div>

    <div class="test-container">
        <div class="test-title">4. CSS实现原理</div>
        <p>当 <code>showExpandIcon: false</code> 时，组件会添加 <code>hide-expand-icon</code> CSS类名，该类名会隐藏以下元素：</p>
        <ul>
            <li><code>.cxd-Nav-Menu-item-arrow</code> - 主要的展开箭头</li>
            <li><code>.cxd-Nav-Menu-item-expand</code> - 展开按钮容器</li>
            <li><code>.cxd-Nav-Menu-item-expand-icon</code> - 展开图标</li>
            <li><code>.cxd-Nav-Menu-submenu-arrow</code> - 子菜单箭头</li>
            <li>其他相关的展开按钮元素</li>
        </ul>
    </div>

    <div class="test-container">
        <div class="test-title">5. 验证方法</div>
        <p><strong>如何验证功能是否正常</strong>：</p>
        <ol>
            <li>创建一个包含子菜单的导航组件</li>
            <li>设置 <code>showExpandIcon: true</code>，确认展开按钮显示</li>
            <li>设置 <code>showExpandIcon: false</code>，确认展开按钮隐藏</li>
            <li>检查菜单项的布局是否正常（没有多余的空白）</li>
            <li>确认子菜单功能仍然可以通过点击菜单项来展开/收起</li>
        </ol>
    </div>
</body>
</html>
