import React from 'react';
import {BasePlugin, getSchemaTpl, registerEditorPlugin} from 'amis-editor-core';
import {getEventControlConfig} from 'amis-editor/lib/renderer/event-control/helper';
import {inputStateTpl} from 'amis-editor/lib/renderer/style-control/helper';
import {link} from 'fs';
import {visibleOn, width} from '@/editor/EChartsEditor/Common';
import Flex from 'amis/lib/renderers/Flex';

export class MyNavPlugin extends BasePlugin {
  static id = 'MyNavPlugin';
  static scene = ['layout'];

  // 关联渲染器名字
  rendererName = 'my-nav-page';
  $schema = '/schemas/MyNavSchema.json';

  // 组件基本信息
  name = '导航';
  panelTitle = '导航组件';
  icon = 'fa fa-bars';
  panelIcon = 'fa fa-bars';
  pluginIcon = 'fa fa-bars';
  isBaseComponent = true;
  panelJustify = true;
  notRenderFormZone = true;

  // 组件描述信息
  description = '用于展示导航菜单，支持多级嵌套、横向/纵向布局等';
  docLink = '/amis/zh-CN/components/nav';
  tags = ['门户'];

  // 组件默认配置
  scaffold = {
    type: 'my-nav-page',
    stacked: false,
    enableItemActions: false,
    showExpandIcon: true,
    header: {
      enable: true,
      showTitle: true,
      title: '导航头部',
      showSearch: false,
      // showAddParent: false,
      showAddNav: true,
      showCollapse: true
    },
    links: [
      {
        label: '首页',
        icon: 'fa fa-home',
        to: '/home'
      },
      {
        label: '产品',
        icon: 'fa fa-cube',
        to: '/products',
        children: [
          {
            label: '产品列表',
            to: '/products/list'
          },
          {
            label: '产品详情',
            to: '/products/detail'
          }
        ]
      },
      {
        label: '关于我们',
        icon: 'fa fa-info-circle',
        to: '/about'
      }
    ]
  };

  // 预览界面
  previewSchema = {
    type: 'my-nav-page',
    className: 'text-left',
    stacked: false,
    links: [
      {
        label: '首页',
        icon: 'fa fa-home',
        to: '/home'
      },
      {
        label: '产品',
        icon: 'fa fa-cube',
        to: '/products'
      }
    ]
  };

  // 添加事件定义
  events = [
    {
      eventName: 'click',
      eventLabel: '菜单项点击',
      description: '点击导航项时触发'
      // ...
    },
    {
      eventName: 'select',
      eventLabel: '菜单项选中',
      description: '选中导航项时触发'
      // ...
    },
    {
      eventName: 'expand',
      eventLabel: '菜单展开',
      description: '菜单展开时触发'
      // ...
    },
    {
      eventName: 'collapse',
      eventLabel: '菜单折叠',
      description: '菜单折叠时触发'
      // ...
    },
    {
      eventName: 'loaded',
      eventLabel: '数据加载完成',
      description: '数据加载完成时触发'
      // ...
    },
    {
      eventName: 'nav-add-item',
      eventLabel: '新增导航项',
      description: '点击新增导航按钮时触发',
      dataSchema: [
        {
          type: 'object',
          properties: {
            newItem: {
              type: 'object',
              title: '新增的导航项',
              properties: {
                label: {type: 'string', title: '菜单名称'},
                icon: {type: 'string', title: '图标'},
                to: {type: 'string', title: '链接地址'}
              }
            },
            newLinks: {
              type: 'array',
              title: '更新后的导航列表'
            }
          }
        }
      ]
    }
  ];

  // 面板配置
  panelBodyCreator = (context: any) => {
    const {id} = context;
    console.log(
      'eventControl',
      getSchemaTpl('editApi', {
        name: 'saveOrderApi',
        label: '顺序保存接口',
        visibleOn: 'this.draggable === true'
      })
    );

    return getSchemaTpl('tabs', [
      {
        title: '属性',
        body: getSchemaTpl(
          'collapseGroup',
          [
            {
              title: '基本',
              body: [
                getSchemaTpl('switch', {
                  name: 'stacked',
                  label: '竖向摆放',
                  value: false
                }),
                {
                  type: 'select',
                  name: 'mode',
                  label: '子菜单展示方式',
                  options: [
                    {
                      label: '内联展示',
                      value: 'inline'
                    },
                    {
                      label: '浮层展示',
                      value: 'float'
                    },
                    {
                      label: '面板展示',
                      value: 'panel'
                    }
                  ],
                  visibleOn: 'this.stacked === true'
                },
                getSchemaTpl('switch', {
                  name: 'accordion',
                  label: '手风琴模式',
                  visibleOn: 'this.stacked === true && this.mode === "inline"'
                }),
                getSchemaTpl('minimum', {
                  name: 'defaultOpenLevel',
                  label: '默认展开层级'
                }),
                getSchemaTpl('maxLength', {
                  name: 'level',
                  label: '最大显示层级'
                }),
                getSchemaTpl('maxLength', {
                  name: 'indentSize',
                  label: '层级缩进值'
                }),
                getSchemaTpl('switch', {
                  name: 'searchable',
                  label: '搜索',
                  value: false
                }),
                getSchemaTpl('tplFormulaControl', {
                  name: 'matchFunc',
                  label: '自定义匹配函数',
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('switch', {
                  name: 'clearable',
                  label: '可清除',
                  value: false,
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('switch', {
                  name: 'searchImmediately',
                  label: '清除后立即搜索',
                  value: false,
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('switch', {
                  name: 'mini',
                  label: 'mini版本',
                  value: false,
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('switch', {
                  name: 'enhance',
                  label: '加强样式',
                  value: false,
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('placeholder', {
                  name: 'placeholder',
                  label: '占位提示',
                  value: '请输入',
                  visibleOn: 'this.searchable === true'
                }),
                getSchemaTpl('switch', {
                  name: 'draggable',
                  label: '是否可拖拽排序',
                  value: false
                }),
                getSchemaTpl('apiControl', {
                  label: '顺序保存接口',
                  name: 'saveOrderApi',
                  visibleOn: 'this.draggable === true',
                  renderLabel: true,
                  showPrefix: false
                }),
                getSchemaTpl('switch', {
                  name: 'dragOnSameLevel',
                  label: '仅允许同层级内拖拽',
                  value: false,
                  visibleOn: 'this.draggable === true'
                }),
                getSchemaTpl('layout:sorption', {
                  name: 'dragPosition',
                  label: '拖拽按钮位置',
                  visibleOn: 'this.draggable !== false',
                  options: [
                    {
                      label: '靠左',
                      value: 'before'
                    },
                    {
                      label: '靠右',
                      value: 'after'
                    }
                  ]
                }),
                getSchemaTpl('switch', {
                  name: 'collapsed',
                  label: '导航缩起',
                  value: false
                }),

                getSchemaTpl('switch', {
                  name: 'showExpandIcon',
                  label: '显示展开按钮',
                  value: true
                }),

                getSchemaTpl('layout:sorption', {
                  name: 'expandPosition',
                  label: '展开按钮位置',
                  visibleOn: 'this.showExpandIcon !== false',
                  options: [
                    {
                      label: '靠左',
                      value: 'before'
                    },
                    {
                      label: '靠右',
                      value: 'after'
                    }
                  ]
                }),

                getSchemaTpl('icon', {
                  name: 'expandIcon',
                  label: '展开按钮图标',
                  description: '支持字符串格式和对象格式的图标',
                  visibleOn: 'this.showExpandIcon !== false'
                })
              ]
            },
            {
              title: '导航头部',
              body: [
                getSchemaTpl('switch', {
                  name: 'header.enable',
                  label: '开启导航头部',
                  value: false
                }),
                getSchemaTpl('switch', {
                  name: 'header.showTitle',
                  label: '标题',
                  value: true,
                  visibleOn: 'this.header && this.header.enable'
                }),
                getSchemaTpl('title', {
                  name: 'header.title',
                  label: '导航标题',
                  placeholder: '请输入导航标题',
                  visibleOn:
                    'this.header && this.header.enable && this.header.showTitle'
                }),
                getSchemaTpl('switch', {
                  name: 'header.showSearch',
                  label: '搜索',
                  value: false,
                  visibleOn: 'this.header && this.header.enable'
                }),
                // getSchemaTpl('switch', {
                //   name: 'header.showAddParent',
                //   label: '新增父级导航',
                //   value: false,
                //   visibleOn: 'this.header && this.header.enable'
                // }),
                getSchemaTpl('switch', {
                  name: 'header.showAddNav',
                  label: '新增导航',
                  value: false,
                  visibleOn: 'this.header && this.header.enable'
                }),
                getSchemaTpl('switch', {
                  name: 'header.showCollapse',
                  label: '收起导航',
                  value: false,
                  visibleOn: 'this.header && this.header.enable'
                })
              ]
            },
            {
              title: '菜单项',
              body: [
                getSchemaTpl('navControl', {
                  name: 'links',
                  onlyLeaf: false,
                  label: '数据'
                }),
                getSchemaTpl('nav-badge', {
                  name: 'itemBadge',
                  label: '角标'
                })
              ]
            },
            {
              title: '导航操作栏',
              body: [
                getSchemaTpl('switch', {
                  name: 'enableItemActions',
                  label: '开启操作栏',
                  value: false
                }),
                {
                  type: 'tpl',
                  tpl: '<div class="cxd-Form-label">按钮管理</div>',
                  visibleOn: 'this.enableItemActions === true'
                },
                getSchemaTpl('combo-container', {
                  label: false,
                  name: 'itemActions',
                  type: 'combo',
                  visibleOn: 'this.enableItemActions === true',
                  inputClassName: 'ae-BulkActions-control',
                  multiple: true,
                  addable: true,
                  removable: true,
                  draggable: true,
                  draggableTip: '',
                  scaffold: {
                    label: '按钮',
                    type: 'button'
                  },
                  value: [
                    {
                      type: 'button',
                      label: '重命名',
                      level: 'link',
                      size: 'sm',
                      actionType: 'custom'
                    },
                    {
                      type: 'button',
                      label: '编辑',
                      level: 'link',
                      size: 'sm',
                      actionType: 'custom'
                    },
                    {
                      type: 'button',
                      label: '删除',
                      level: 'danger',
                      size: 'sm',
                      actionType: 'custom',
                      confirmText: '确定要删除这个菜单项吗？'
                    }
                  ],
                  items: [
                    getSchemaTpl('tpl:btnLabel'),
                    {
                      columnClassName: 'p-t-xs col-edit',
                      children: ({index}: any) => (
                        <button
                          onClick={this.handleItemActionEdit.bind(
                            this,
                            id,
                            index
                          )}
                          data-tooltip="修改"
                          data-position="bottom"
                          className="text-muted"
                        >
                          <i className="fa fa-pencil" />
                        </button>
                      )
                    }
                  ]
                })
              ]
            },
            {
              title: '状态',
              body: [...getSchemaTpl('status').body]
            }
          ],
          {...context?.schema, configTitle: 'props'}
        )
      },
      {
        title: '外观',
        className: 'p-none',
        body: [
          getSchemaTpl('collapseGroup', [
            ...getSchemaTpl('theme:base', {
              classname: 'baseControlClassName',
              title: '基本样式'
            }),
            ...getSchemaTpl('theme:base', {
              classname: 'headerControlClassName',
              title: '导航头部样式'
            }),
            ...getSchemaTpl('theme:base', {
              classname: 'headerTitleControlClassName',
              title: '导航头部标题样式',
              extra: [
                getSchemaTpl('theme:font', {
                  label: '文字',
                  name: 'themeCss.headerTitleControlClassName.font'
                })
              ]
            }),
            ...getSchemaTpl('theme:base', {
              classname: 'navBtnControlClassName',
              title: '导航头部按钮样式',
              extra: [
                getSchemaTpl('theme:colorPicker', {
                  name: 'themeCss.navBtnControlClassName.--icon-color',
                  label: '图标颜色',
                  pipeIn: (value: any) => {
                    return value || '#666';
                  },
                  pipeOut: (value: any) => {
                    return value || '#666';
                  }
                }),
                // getSchemaTpl('theme:colorPicker', {
                //   name: 'themeCss.navBtnControlClassName.--icon-fill-color',
                //   label: '图标填充颜色',
                //   pipeIn: (value: any) => {
                //     return value || 'transparent';
                //   },
                //   pipeOut: (value: any) => {
                //     return value || 'transparent';
                //   }
                // }),
                getSchemaTpl('layout:width:v2', {
                  name: 'themeCss.navBtnControlClassName.--icon-size',
                  label: '图标大小',
                  value: '24px',
                  unitOptions: ['px', 'rem', 'em'],
                  pipeIn: (value: any) => {
                    // 确保值包含单位
                    if (!value) return '24px';
                    return value.toString().match(/\d+$/)
                      ? `${value}px`
                      : value;
                  },
                  pipeOut: (value: any) => {
                    // 确保值包含单位
                    if (!value) return '24px';
                    return value.toString().match(/\d+$/)
                      ? `${value}px`
                      : value;
                  }
                }),
                {
                  type: 'input-number',
                  name: 'themeCss.navBtnControlClassName.--button-size',
                  label: '按钮大小',
                  value: 36,
                  min: 20,
                  max: 60,
                  step: 1,
                  unit: 'px',
                  pipeIn: (value: any) => {
                    if (!value) return 36;
                    const match =
                      typeof value === 'string' ? value.match(/(\d+)/) : null;
                    return match ? parseInt(match[1], 10) : 36;
                  },
                  pipeOut: (value: any) => {
                    return `${value}px`;
                  }
                }
              ]
            }),
            ...getSchemaTpl('theme:base', {
              classname: 'navContentClassName',
              title: '导航内容样式'
            }),
            ...getSchemaTpl('theme:base', {
              classname: 'navItemClassName',
              title: '导航项样式',
              extra: [
                getSchemaTpl('theme:font', {
                  label: '文字',
                  name: 'themeCss.navItemClassName.font'
                })
              ]
            }),
            {
              title: '搜索样式',
              body: [
                ...inputStateTpl('themeCss.searchInputClassName', '搜索输入框')
              ]
            }
          ])
        ]
      },
      {
        title: '事件',
        className: 'p-none',
        body: [
          getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context)
          })
        ]
      }
    ]);
  };

  // 处理操作按钮编辑
  handleItemActionEdit(id: string, index: number) {
    const store = this.manager.store;
    const schema = store.getSchema(id);
    const action = schema?.itemActions[index];

    if (action && action.$$id) {
      store.setActiveId(action.$$id);
    }
  }
}

// 注册插件
registerEditorPlugin(MyNavPlugin);
