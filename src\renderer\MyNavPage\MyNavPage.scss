// MyNav 导航组件样式
.my-nav-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100px;
  overflow: visible;
  position: relative;

  // 确保头部占满整个宽度
  > .nav-header {
    width: 100% !important;
    margin: 0;
    padding-left: 16px;
    padding-right: 16px;
  }
}

// 导航头部样式 - 通过CSS变量应用主题样式
.my-nav-wrapper .nav-header,
.nav-header {
  // 基础布局样式（强制）
  padding: 12px 16px !important;
  min-height: 48px !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  box-sizing: border-box !important;
  position: relative !important;
  table-layout: fixed !important; // 使用表格布局确保稳定

  // 主题样式通过CSS变量应用（不使用!important，让CSS变量生效）
  background-color: var(--background-color-default);
  background: var(--background-color-default);
  color: var(--color-default);

  // 边框样式通过CSS变量控制（amis主题系统生成的变量）
  border: var(--border-width-default, 0) var(--border-style-default, solid) var(--border-color-default, transparent);

  // 悬浮状态
  &:hover {
    background-color: var(--background-color-hover);
    background: var(--background-color-hover);
    color: var(--color-hover);
    border: var(--border-width-hover, var(--border-width-default, 0)) var(--border-style-hover, var(--border-style-default, solid)) var(--border-color-hover, var(--border-color-default, transparent));
  }

  // 激活状态
  &:active,
  &.is-active {
    background-color: var(--background-color-active);
    background: var(--background-color-active);
    color: var(--color-active);
    border: var(--border-width-active, var(--border-width-default, 0)) var(--border-style-active, var(--border-style-default, solid)) var(--border-color-active, var(--border-color-default, transparent));
  }

  // 焦点状态
  &:focus,
  &.is-focused {
    background-color: var(--background-color-focused, var(--background-color-active));
    background: var(--background-color-focused, var(--background-color-active));
    color: var(--color-focused, var(--color-active));
    border: var(--border-width-focused, var(--border-width-active, var(--border-width-default, 0))) var(--border-style-focused, var(--border-style-active, var(--border-style-default, solid))) var(--border-color-focused, var(--border-color-active, var(--border-color-default, transparent)));
  }

  // 强制所有状态保持布局
  &,
  &:hover,
  &:focus,
  &:active,
  &.is-hover,
  &.is-active,
  &.is-focused {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    box-sizing: border-box !important;
    table-layout: fixed !important;
  }
}

.my-nav-wrapper .nav-header .nav-header-content,
.nav-header .nav-header-content {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
  min-height: 32px !important;
  flex: 1 !important;
  table-layout: fixed !important; // 使用表格布局确保稳定

  // 强制所有状态保持布局
  &,
  &:hover,
  &:focus,
  &:active,
  &.is-hover,
  &.is-active,
  &.is-focused {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    flex: 1 !important;
    table-layout: fixed !important;
  }
}

.my-nav-wrapper .nav-header .nav-header-title,
.nav-header .nav-header-title {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: var(--text-color, #333) !important;
  flex: 1 !important;
  line-height: 1.5 !important;

  // 支持导航头部标题的完整字体样式
  font-family: var(--font-family-default) !important;
  text-decoration: var(--text-decoration-default) !important;
  text-decoration-line: var(--text-decoration-line-default) !important;
  text-decoration-style: var(--text-decoration-style-default) !important;
  text-decoration-color: var(--text-decoration-color-default) !important;

  // 文本对齐样式支持 - 使用正确的CSS变量名
  text-align: var(--text-align) !important;
  vertical-align: var(--vertical-align) !important;
}

// 导航头部标题样式类支持
[class*="headerTitleControlClassName"] {
  // 确保文本装饰样式能够正确应用
  color: var(--color-default) !important;
  font-family: var(--font-family-default) !important;
  font-size: var(--font-size-default) !important;
  font-weight: var(--font-weight-default) !important;
  line-height: var(--line-height-default) !important;
  text-decoration: var(--text-decoration-default) !important;
  text-decoration-line: var(--text-decoration-line-default) !important;
  text-decoration-style: var(--text-decoration-style-default) !important;
  text-decoration-color: var(--text-decoration-color-default) !important;
  text-decoration-thickness: var(--text-decoration-thickness-default) !important;

  // 文本对齐样式支持 - 使用正确的CSS变量名
  text-align: var(--text-align) !important;
  vertical-align: var(--vertical-align) !important;

  // 悬浮状态
  &:hover {
    color: var(--color-hover, var(--color-default)) !important;
    text-decoration: var(--text-decoration-hover, var(--text-decoration-default)) !important;
    text-decoration-line: var(--text-decoration-line-hover, var(--text-decoration-line-default)) !important;
    text-decoration-style: var(--text-decoration-style-hover, var(--text-decoration-style-default)) !important;
    text-decoration-color: var(--text-decoration-color-hover, var(--text-decoration-color-default)) !important;
    text-align: var(--text-align) !important;
    vertical-align: var(--vertical-align) !important;
  }

  // 激活状态
  &.is-active,
  &.is-opened {
    color: var(--color-active, var(--color-default)) !important;
    text-decoration: var(--text-decoration-active, var(--text-decoration-default)) !important;
    text-decoration-line: var(--text-decoration-line-active, var(--text-decoration-line-default)) !important;
    text-decoration-style: var(--text-decoration-style-active, var(--text-decoration-style-default)) !important;
    text-decoration-color: var(--text-decoration-color-active, var(--text-decoration-color-default)) !important;
    text-align: var(--text-align) !important;
    vertical-align: var(--vertical-align) !important;
  }
  min-height: 21px !important;
  display: flex !important;
  align-items: center !important;
  margin-right: 16px !important;

  // 移除文字装饰（下划线等）
  text-decoration: none !important;

  // 强制所有状态保持布局
  &,
  &:hover,
  &:focus,
  &:active,
  &.is-hover,
  &.is-active,
  &.is-focused {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
  }

  span {
    display: inline-block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 100% !important;
    text-decoration: none !important;

    // 强制所有状态保持文字样式
    &,
    &:hover,
    &:focus,
    &:active,
    &.is-hover,
    &.is-active,
    &.is-focused {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      max-width: 100% !important;
      text-decoration: none !important;
    }
  }

  // 如果标题是链接，移除链接的下划线
  a {
    text-decoration: none !important;
    color: inherit !important;

    &:hover,
    &:focus,
    &:active,
    &:visited {
      text-decoration: none !important;
      color: inherit !important;
    }
  }
}

.my-nav-wrapper .nav-header .nav-header-actions,
.nav-header .nav-header-actions {
  display: flex !important;
  align-items: center !important;
  gap: var(--button-gap, 8px) !important;
  flex-shrink: 0 !important;
  margin-left: auto !important;
  width: auto !important; // 确保宽度自适应
  min-width: 40px !important; // 设置最小宽度

  // 强制所有状态保持布局
  &,
  &:hover,
  &:focus,
  &:active,
  &.is-hover,
  &.is-active,
  &.is-focused {
    display: flex !important;
    align-items: center !important;
    flex-shrink: 0 !important;
    margin-left: auto !important;
    width: auto !important;
    min-width: 40px !important;
  }
}

// 按钮基础样式 - 应用navBtnControlClassName的主题样式
.search-btn,
.add-nav-btn,
.collapse-btn {
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  // 基础尺寸，使用CSS变量控制
  width: var(--button-size, 36px) !important;
  height: var(--button-size, 36px) !important;
  min-width: var(--button-size, 36px) !important;
  min-height: var(--button-size, 36px) !important;
  padding: 8px !important;
  border-radius: 4px !important;
  
  // 应用CSS变量样式 - 颜色仅应用于文本，不应用于SVG
  color: var(--icon-color, #666) !important;
  font-size: var(--icon-size, 16px) !important;

  // 确保所有状态保持样式
  &,
  &:hover,
  &:focus,
  &:active,
  &.is-hover,
  &.is-active,
  &.is-focused {
    width: var(--button-size, 36px) !important;
    height: var(--button-size, 36px) !important;
    min-width: var(--button-size, 36px) !important;
    min-height: var(--button-size, 36px) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: var(--icon-color, #666) !important;
    font-size: var(--icon-size, 16px) !important;
  }

  svg {
    // 使用绝对尺寸来确保图标大小正确
    width: var(--icon-size, 16px) !important;
    height: var(--icon-size, 16px) !important;
    // 分离填充色和描边色
    fill: var(--icon-fill-color, transparent) !important;
    stroke: var(--icon-color, #666) !important;
    stroke-width: 1.5 !important;
    flex-shrink: 0 !important;
    
    // 确保所有路径和元素都使用正确的颜色
    path, circle, rect, line, polyline, polygon {
      fill: var(--icon-fill-color, transparent) !important;
      stroke: var(--icon-color, #666) !important;
    }
    
    // 特殊情况：如果有需要填充的图标元素，可以添加.fill-icon类
    .fill-icon {
      fill: var(--icon-color, #666) !important;
      stroke: none !important;
    }
    
    // 特定图标样式
    &.search-icon {
      circle, path {
        stroke: var(--icon-color, #666) !important;
        fill: var(--icon-fill-color, transparent) !important;
      }
    }
    
    &.add-icon {
      path {
        stroke: var(--icon-color, #666) !important;
        fill: var(--icon-fill-color, transparent) !important;
      }
    }
    
    &.collapse-icon {
      path {
        stroke: var(--icon-color, #666) !important;
        fill: var(--icon-fill-color, transparent) !important;
      }
    }
  }
}

// 收起导航按钮特殊样式
.collapse-btn {
  svg {
    transition: transform 0.2s ease !important;
  }

  &.collapsed {
    svg {
      transform: rotate(180deg) !important;
    }
  }
}

// 隐藏展开按钮的样式
.hide-expand-icon {
  // 隐藏amis nav组件的展开按钮
  .cxd-Nav-Menu-item-arrow,
  .cxd-Nav-Menu-item-expand,
  .cxd-Nav-Menu-item-expand-icon,
  .cxd-Nav-Menu-submenu-arrow,
  .nav-item-expand-icon,
  .nav-expand-icon {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    overflow: hidden !important;
  }

  // 同时隐藏可能的其他展开按钮样式
  .cxd-Nav-Menu-item {
    &::before,
    &::after {
      display: none !important;
    }
  }

  // 确保菜单项布局正常
  // .cxd-Nav-Menu-item-link {
  //   padding-left: 1rem !important;
  // }
}

// HTML图标样式
.nav-item-html-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: transparent !important;

  // 确保HTML图标中的span元素正确显示
  span {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: transparent !important;
    background-color: transparent !important;

    // 确保SVG在span中正确显示
    svg {
      width: 24px !important;
      height: 24px !important;
      flex-shrink: 0 !important;
    }
  }
}

// 通用图标背景移除样式 - 确保所有导航图标都没有背景
.cxd-Nav-Menu-item,
.cxd-Nav-Menu-submenu {
  // 移除图标容器的背景
  .cxd-Nav-Menu-item-icon,
  .nav-item-icon,
  .nav-item-html-icon,
  .nav-item-svg-icon {
    background: transparent !important;
    background-color: transparent !important;

    // 移除span容器的背景
    span {
      background: transparent !important;
      background-color: transparent !important;
    }

    // 移除SVG的背景
    svg {
      background: transparent !important;
      background-color: transparent !important;
    }
  }

  // 移除tpl类型图标的背景
  .cxd-TplField {
    background: transparent !important;
    background-color: transparent !important;

    span {
      background: transparent !important;
      background-color: transparent !important;
    }

    svg {
      background: transparent !important;
      background-color: transparent !important;
    }
  }
}

// SVG图标样式
.nav-item-svg-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;

  // 直接的SVG元素样式
  svg {
    width: 16px !important;
    height: 16px !important;
    flex-shrink: 0 !important;

    // 确保SVG使用当前文本颜色
    color: currentColor !important;

    // 对于使用stroke的SVG
    * {
      stroke: currentColor !important;
    }

    // 对于使用fill的SVG
    *[fill="currentColor"] {
      fill: currentColor !important;
    }
  }
}

// 带图标的导航项样式
.nav-item-with-icon {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;

  // 图标容器样式
  > span:first-child {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;

    // 处理span包装的SVG图标
    svg {
      width: 16px !important;
      height: 16px !important;
      flex-shrink: 0 !important;
    }
  }

  // 文本样式
  .nav-item-text {
    flex: 1 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }
}

// 自定义渲染的导航项样式
.nav-item-custom-render {
  width: 100% !important;

  .nav-item-with-icon {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;

    // 确保图标正确显示
    > span:first-child {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      flex-shrink: 0 !important;

      svg {
        width: 16px !important;
        height: 16px !important;
        flex-shrink: 0 !important;
      }
    }

    // 文本样式
    .nav-item-text {
      flex: 1 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
  }
}

// 导航内容区域 - 应用navContentClassName的主题样式
.my-nav-wrapper .my-nav-content,
// 导航包装器收缩样式
.my-nav-wrapper {
  transition: all 0.3s ease !important;
  overflow: visible !important;

  &.collapsed {
    width: 48px !important; // 收缩后的宽度，只显示一个展开按钮
    min-width: 48px !important;

    // 导航头部收缩
    .nav-header {
      padding: 8px 0 !important;

      .nav-header-title {
        opacity: 0 !important;
        width: 0 !important;
        overflow: hidden !important;
        white-space: nowrap !important;
      }

      .nav-header-actions {
        justify-content: center !important;
        padding: 0 !important;

        // 隐藏搜索按钮 - 使用更强的选择器
        .search-btn,
        button.search-btn {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          width: 0 !important;
          height: 0 !important;
          overflow: hidden !important;
        }

        // 隐藏加号按钮 - 使用更强的选择器
        .add-nav-btn,
        button.add-nav-btn {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          width: 0 !important;
          height: 0 !important;
          // overflow: hidden !important;
        }

        // 只保留收缩按钮，完美居中
        .collapse-btn,
        button.collapse-btn {
          margin: 0 !important;
          width: 32px !important;
          height: 32px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
      }
    }

    // 导航内容收缩 - 隐藏菜单项文字
    .my-nav-content {
      .cxd-Nav {
        .cxd-Nav-Menu {
          .cxd-Nav-Menu-item {
            .cxd-Nav-Menu-item-link {
              justify-content: center !important;

              .cxd-Nav-Menu-item-label {
                opacity: 0 !important;
                width: 0 !important;
                // overflow: hidden !important;
                margin-left: 0 !important;
              }

              .cxd-Nav-Menu-item-icon {
                margin-right: 0 !important;
              }
            }
          }

          // 子菜单项也要处理
          .cxd-Nav-Menu-submenu {
            .cxd-Nav-Menu-item-link {
              justify-content: center !important;

              .cxd-Nav-Menu-item-label {
                opacity: 0 !important;
                width: 0 !important;
                overflow: hidden !important;
              }
            }
          }
        }
      }
    }
  }
}

.my-nav-content {
  flex: 1 !important;
  overflow: visible !important;
  transition: all 0.3s ease !important;
  min-height: 0 !important;
  width: 100% !important;
  position: relative !important;
}

.cxd-Nav,
.dark-Nav{
  background: transparent !important;
}

// 搜索样式通过searchClassName应用
.cxd-Nav--searchable {
  .cxd-Nav-searchbox,
  .cxd-SearchBox,
  [class*="searchClassName"] {
    padding: 8px !important;
    margin-bottom: 8px !important;
    
    // 搜索输入框
    .cxd-TextControl {
      // 应用CSS变量
      --input-bg: var(--background-color-default, #fff);
      --input-color: var(--text-color, #333);
      --input-border-color: var(--border-color-default, #ddd);
      --input-border-radius: var(--border-radius-default, 4px);
      --input-border-width: var(--border-width-default, 1px);
      --input-border-style: var(--border-style-default, solid);
      --input-placeholder-color: var(--color-placeholder, #999);
      --input-hover-bg: var(--background-color-hover, #f7f7f7);
      --input-hover-border-color: var(--border-color-hover, #999);
      --input-font-size: var(--input-font-size, 12px);
      --input-height: var(--input-height, 30px);
      
      // 设置基础样式
      background-color: var(--input-bg) !important;
      color: var(--input-color) !important;
      border: var(--input-border-width) var(--input-border-style) var(--input-border-color) !important;
      border-radius: var(--input-border-radius) !important;
      height: var(--input-height) !important;
      min-height: var(--input-height) !important;
      
      // 悬浮状态
      &:hover {
        background-color: var(--input-hover-bg) !important;
        border-color: var(--input-hover-border-color) !important;
      }
      
      // 聚焦状态
      &:focus, &.is-focused {
        border-color: var(--primary-color, #2468f2) !important;
        box-shadow: 0 0 0 2px rgba(36, 104, 242, 0.2) !important;
      }
      
      // 输入框内部元素
      input {
        color: var(--input-color) !important;
        font-size: var(--input-font-size) !important;
        height: calc(var(--input-height) - 2px) !important;
        
        &::placeholder {
          color: var(--input-placeholder-color) !important;
          font-size: var(--input-font-size) !important;
        }
      }
      
      // 清除按钮
      .cxd-TextControl-clear {
        color: var(--icon-color, #999) !important;
      }
    }
  }
}

// 增强搜索框样式 - 支持mini和enhance模式
.cxd-Nav--searchable {
  &.cxd-Nav--mini {
    .cxd-Nav-searchbox,
    .cxd-SearchBox,
    [class*="searchClassName"] {
      padding: 4px !important;
      margin-bottom: 4px !important;
      
      .cxd-TextControl {
        height: 28px !important;
        min-height: 28px !important;
      }
    }
  }
  
  &.cxd-Nav--enhance {
    .cxd-Nav-searchbox,
    .cxd-SearchBox,
    [class*="searchClassName"] {
      background-color: var(--background-color-default, rgba(0, 0, 0, 0.03)) !important;
      border-radius: var(--border-radius-default, 4px) !important;
      
      .cxd-TextControl {
        border: none !important;
        background-color: transparent !important;
        
        &:hover, &:focus, &.is-focused {
          background-color: transparent !important;
          box-shadow: none !important;
        }
      }
    }
  }
}

// 确保搜索框在各种主题下都能正确显示
.cxd-Nav--searchable.cxd-Nav--dark {
  .cxd-Nav-searchbox,
  .cxd-SearchBox,
  [class*="searchClassName"] {
    .cxd-TextControl {
      --input-bg: rgba(255, 255, 255, 0.1);
      --input-color: rgba(255, 255, 255, 0.85);
      --input-border-color: rgba(255, 255, 255, 0.2);
      --input-placeholder-color: rgba(255, 255, 255, 0.3);
      --input-hover-bg: rgba(255, 255, 255, 0.15);
      --input-hover-border-color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 搜索输入框字体样式支持 - 根据实际HTML结构调整选择器
[class*="searchInputClassName"] {
  // 直接选择input元素
  input[type="text"] {
    // 确保字体样式能够正确应用
    color: var(--color-default) !important;
    font-family: var(--font-family-default) !important;
    font-size: var(--font-size-default) !important;
    font-weight: var(--font-weight-default) !important;
    line-height: var(--line-height-default) !important;

    // 悬浮状态
    &:hover {
      color: var(--color-hover, var(--color-default)) !important;
    }

    // 聚焦状态
    &:focus,
    &.is-focused {
      color: var(--color-active, var(--color-default)) !important;
    }
  }
}

// 兼容其他可能的搜索框结构
.cxd-SearchBox.cxd-Nav-SearchBox {
  &[class*="searchInputClassName"] {
    input[type="text"] {
      color: var(--color-default) !important;
      font-family: var(--font-family-default) !important;
      font-size: var(--font-size-default) !important;
      font-weight: var(--font-weight-default) !important;
      line-height: var(--line-height-default) !important;

      &:hover {
        color: var(--color-hover, var(--color-default)) !important;
      }

      &:focus,
      &.is-focused {
        color: var(--color-active, var(--color-default)) !important;
      }
    }
  }
}

// 最具体的选择器 - 直接匹配实际HTML结构
div.cxd-SearchBox.cxd-Nav-SearchBox[class*="searchInputClassName"] {
  // 支持自定义CSS变量
  input[type="text"] {
    color: var(--search-text-color, var(--color-default)) !important;
    font-family: var(--font-family-default) !important;
    font-size: var(--search-font-size, var(--font-size-default)) !important;
    font-weight: var(--search-font-weight, var(--font-weight-default)) !important;
    line-height: var(--search-line-height, var(--line-height-default)) !important;

    &:hover {
      color: var(--color-hover, var(--search-text-color, var(--color-default))) !important;
    }

    &:focus,
    &.is-focused {
      color: var(--color-active, var(--search-text-color, var(--color-default))) !important;
    }
  }
}

// 导航项样式支持 - 基于实际HTML结构
.cxd-Nav {
  // 父级菜单项（有子菜单的项）- cxd-Nav-Menu-submenu
  .cxd-Nav-Menu-submenu[class*="navItemClassName"] {
    // 应用样式到submenu-title内的文字
    .cxd-Nav-Menu-submenu-title {
      .cxd-Nav-Menu-item-wrap {
        .cxd-Nav-Menu-item-link {
          .cxd-Nav-Menu-item-label {
            color: var(--color-default) !important;
            font-family: var(--font-family-default) !important;
            font-size: var(--font-size-default) !important;
            font-weight: var(--font-weight-default) !important;
            line-height: var(--line-height-default) !important;
          }
        }
      }
    }

    // 悬浮状态
    &:hover {
      .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
        color: var(--color-hover, var(--color-default)) !important;
      }
    }

    // 选中状态
    &.cxd-Nav-Menu-submenu-selected {
      .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
        color: var(--color-active, var(--color-default)) !important;
      }
    }
  }

  // 普通菜单项（无子菜单的项）- cxd-Nav-Menu-item
  .cxd-Nav-Menu-item[class*="navItemClassName"] {
    .cxd-Nav-Menu-item-wrap {
      .cxd-Nav-Menu-item-link {
        .cxd-Nav-Menu-item-label {
          color: var(--color-default) !important;
          font-family: var(--font-family-default) !important;
          font-size: var(--font-size-default) !important;
          font-weight: var(--font-weight-default) !important;
          line-height: var(--line-height-default) !important;
          text-align: var(--text-align) !important;
          vertical-align: var(--vertical-align) !important;
        }
      }
    }
  }
}

// 更通用的导航项样式选择器 - 基于实际HTML结构
[class*="navItemClassName"] {
  // 应用到父级菜单项（有子菜单）
  &.cxd-Nav-Menu-submenu {
    .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
      color: var(--color-default) !important;
      font-family: var(--font-family-default) !important;
      font-size: var(--font-size-default) !important;
      font-weight: var(--font-weight-default) !important;
      line-height: var(--line-height-default) !important;
      text-align: var(--text-align) !important;
      vertical-align: var(--vertical-align) !important;
    }

    &:hover .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
      color: var(--color-hover, var(--color-default)) !important;
    }

    &.cxd-Nav-Menu-submenu-selected .cxd-Nav-Menu-submenu-title .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
      color: var(--color-active, var(--color-default)) !important;
    }
  }

  // 应用到普通菜单项（无子菜单）
  &.cxd-Nav-Menu-item {
    .cxd-Nav-Menu-item-wrap .cxd-Nav-Menu-item-link .cxd-Nav-Menu-item-label {
      color: var(--color-default) !important;
      font-family: var(--font-family-default) !important;
      font-size: var(--font-size-default) !important;
      font-weight: var(--font-weight-default) !important;
      line-height: var(--line-height-default) !important;
      text-align: var(--text-align) !important;
      vertical-align: var(--vertical-align) !important;
    }
  }
}

// 悬浮框样式
.nav-add-float-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent; // 移除半透明背景
  z-index: 9999;
  pointer-events: none; // 让背景不拦截点击事件
}

.nav-add-float-panel {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 12px; // 减少内边距
  width: 280px; // 固定宽度
  overflow: visible; // 移除滚动条
  pointer-events: auto; // 恢复悬浮框的点击事件
  border: 1px solid #e0e0e0;



  // 分类标题样式
  .nav-add-category {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .nav-add-category-title {
      font-size: 12px;
      color: #999;
      margin-bottom: 6px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .nav-add-category-items {
      display: flex;
      flex-direction: column;
      gap: 2px; // 减少间距
    }
  }

  // 菜单项样式
  .nav-add-menu-item {
    display: flex;
    align-items: center;
    padding: 6px 12px; // 减少垂直内边距
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px; // 设置固定最小高度

    &:hover {
      background-color: #f5f5f5;
    }

    .nav-add-menu-icon {
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-size: 13px;
      flex-shrink: 0;

      i {
        font-size: 13px;
      }
    }

    .nav-add-menu-label {
      font-size: 13px;
      color: #333;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }
  }
}
