# 展开按钮配置功能

## 新增功能

### 1. 显示展开按钮配置项
- **配置名称**: `showExpandIcon`
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 控制是否显示展开按钮

### 2. 配置项联动
- 当 `showExpandIcon` 为 `false` 时，展开按钮位置和图标配置项会隐藏
- 当 `showExpandIcon` 为 `true` 时，显示相关配置项

## 修改内容

### 1. 编辑器插件 (`src/editor/Portlet/MyNavPage/myNavPage.tsx`)
- 添加了 `showExpandIcon` 开关配置项
- 为展开按钮位置和图标配置添加了 `visibleOn` 条件
- 在默认配置中设置 `showExpandIcon: true`

### 2. MyNavPage组件 (`src/renderer/MyNavPage/MyNavPage.tsx`)
- 添加了 `showExpandIcon?: boolean` 类型定义
- 修改了 `processExpandIcon` 方法，支持 `showExpandIcon` 参数
- 当 `showExpandIcon` 为 `false` 时，返回 `null` 不显示展开按钮

### 3. MyNav组件 (`src/renderer/MyNav/MyNav.tsx`)
- 添加了 `showExpandIcon?: boolean` 类型定义
- 修改了 `processExpandIcon` 方法，支持 `showExpandIcon` 参数
- 当 `showExpandIcon` 为 `false` 时，返回 `null` 不显示展开按钮

## 使用方法

### 在编辑器中配置
1. 选择导航组件
2. 在属性面板的"基本"配置中找到"显示展开按钮"开关
3. 关闭开关后，展开按钮位置和图标配置项会自动隐藏
4. 开启开关后，可以继续配置展开按钮的位置和图标

### 在代码中配置
```javascript
{
  type: 'my-nav-page',
  showExpandIcon: false, // 不显示展开按钮
  // 其他配置...
}
```

或者

```javascript
{
  type: 'my-nav-page',
  showExpandIcon: true, // 显示展开按钮（默认值）
  expandPosition: 'after', // 展开按钮位置
  expandIcon: '<svg>...</svg>', // 自定义展开图标
  // 其他配置...
}
```

## 功能特点

1. **向后兼容**: 默认值为 `true`，不影响现有组件
2. **智能联动**: 配置项根据开关状态自动显示/隐藏
3. **完整支持**: 同时支持对象格式和字符串格式的展开图标
4. **统一处理**: MyNav 和 MyNavPage 组件都支持此配置
